{"name": "portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@tabler/icons-react": "^3.12.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "framer-motion": "^11.3.28", "lucide-react": "^0.427.0", "next": "14.2.5", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.52.2", "react-icons": "^5.3.0", "react-transition-group": "^4.4.5", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.5", "eslint-config-prettier": "^9.1.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}