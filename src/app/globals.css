@tailwind base;
@tailwind components;
@tailwind utilities;

/* :root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
} */
/* 
@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
} */

body {
  @apply font-primary bg-primary leading-loose;
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* styles/globals.css or styles/NavBar.module.css */

.nav-link {
  position: relative;
  display: inline-block;
  padding-bottom: 2px; /* Adjust padding for space between text and underline */
}

.nav-link::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 2px; /* Underline height */
  background-color: #3b82f6; /* Underline color (tailwind red-400) */
  transition: width 0.3s ease; /* Animation duration and easing */
}

.nav-link:hover::after {
  width: 100%;
  color: #3b82f6;
}

.word-link:hover {
  color: #3b82f6; /* Change text color on hover */
}
.fade-enter {
  opacity: 0;
  transform: translateY(10px);
}
.fade-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 250ms, transform 250ms;
}
.fade-exit {
  opacity: 1;
}
.fade-exit-active {
  opacity: 0;
  transition: opacity 250ms;
}