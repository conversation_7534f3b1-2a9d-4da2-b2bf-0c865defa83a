{"projects": [{"id": 1, "title": "Portfolio Website", "description": "A personal portfolio website built using Next.Js and TailwindCSS to showcase my projects and skills.", "techStack": ["Next.Js", "Tailwind CSS", "TypeScript"], "githubLink": "https://github.com/AashishTangnami/portfolio", "externalLink": "https://aashishtangnami.vercel.app", "backgroundImage": ""}, {"id": 2, "title": "Big Data Architecture", "description": "A website to understand the architecture of HADOOP, HIVE, SPARK, and related tools.", "techStack": ["JavaScript", "Python", "<PERSON><PERSON>", "PySpark", "Hive"], "githubLink": "https://github.com/AashishTangnami/bigdata-architecture", "externalLink": "https://bigdata-architecture.vercel.app", "backgroundImage": ""}, {"id": 3, "title": "Brain Tumor Classifier", "description": "Brain Tumor Detection by fine tuning a pre-trained model Efficientnet_V2_S using Pytorch with streamlit as frontend.", "techStack": ["Python", "Pytor<PERSON>", "HuggingFace", "Streamlit"], "githubLink": "https://github.com/AashishTangnami/BrainTumorDetection", "externalLink": "https://huggingface.co/spaces/AashishTangnami/<PERSON>_Tumor_Detection", "backgroundImage": ""}, {"id": 4, "title": "Data Engineering: ETL Pipeline", "description": "A production ready ETL pipeline using Apache Spark on Databricks to process and transform data from DataLake or Delta Lake.", "techStack": ["Python", "<PERSON><PERSON>", "PySpark", "DataBricks", "Jupyter Notebook"], "githubLink": "https://github.com/AashishTangnami/Apache-Spark-Data-Engineering", "externalLink": "#", "backgroundImage": ""}, {"id": 5, "title": "Terakoya Academia Inc", "description": "This project was an inhouse project for Terakoya Academia Inc. Firstly, it was built in Django later migrated into php.", "techStack": ["Python", "Django", "React.Js"], "githubLink": "#", "externalLink": "https://www.terakoya-academia.com/welcome", "backgroundImage": "#"}, {"id": 6, "title": "ETL Pipeline", "description": "Solving the problem of data silos by building a scalable ETL pipeline to automate the process of ETL to downstreaming.", "techStack": ["Rust", "<PERSON>er", "PostgreSQL"], "githubLink": "#", "externalLink": "#", "backgroundImage": "#"}, {"id": 7, "title": "Leetcode + HackerRank Solutions", "description": "This is a repository of my solutions to LeetCode/ Hacker Rank problems. I have solved 50+ SQL problems and still solving.", "techStack": ["SQL", "Python"], "githubLink": "https://github.com/AashishTangnami/leetcode", "externalLink": "https://leetcode.com/u/AashishTangnami/", "backgroundImage": "#"}]}